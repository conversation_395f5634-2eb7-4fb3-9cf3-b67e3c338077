@extends('website.layout.master')

@section('content')
    <section class="sec-1 home-banner"
        style="background-image: url('{{ asset('website') . '/' . $page->banner_image ?? '' }}'); background-size: cover; background-position: center;">
        <div class="container h-100">
            <div class="row h-100">
                <div class="col-xxl-8 col-xl-7 d-flex flex-column justify-content-center banner-bg">
                    <div class="home-content">
                        <p class="fs-15 letter-space regular steel-blue text-uppercase">{{ $page->kicker ?? '' }}</p>
                        <h1 class="sora dark-blue para-width">
                            @if ($page && $page->heading_one)
                                @php
                                    $words = explode(' ', trim($page->heading_one));
                                    $lastWord = array_pop($words);
                                    $remainingWords = implode(' ', $words);
                                @endphp
                                {{ $remainingWords }} <span class="gradient_heading">{{ $lastWord }}</span>
                            @endif
                            {{ $page->heading_two ?? '' }}
                        </h1>
                        <p class="light-gray fs-20 normal">{{ $page->description ?? '' }}</p>

                        <div class="white-box">
                            @include('layouts.includes.search-box')
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <figure class="image-box">
                        <img src="{{ asset('website') . '/' . $page->image ?? '' }}"
                            class="home-card-image position-absolute" alt="home-card-image">
                    </figure>
                </div>
            </div>
        </div>
    </section>
    <section class="analysis padding-block">
        <div class="container">
            <div class="row row-gap-5">
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="d-flex align-items-center gap-3 gray-box">
                        @include('svg.stars')
                        <div class="">
                            <h5 class="dark-blue" data-kt-countup="true" data-kt-countup-value="4000"
                                data-kt-countup-suffix="+">0
                            </h5>
                            <p class="m-0 fs-16 light-gray">Verified stylists</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="d-flex align-items-center gap-3 gray-box">
                        @include('svg.search')
                        <div class="">
                            <h5 class="dark-blue" data-kt-countup="true" data-kt-countup-value="2000"
                                data-kt-countup-suffix="+">0
                            </h5>
                            <p class="m-0 fs-16 light-gray">Using stylenest</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="d-flex align-items-center gap-3 gray-box">
                        @include('svg.bookmark')
                        <div class="">
                            <h5 class="dark-blue" data-kt-countup="true" data-kt-countup-value="1000"
                                data-kt-countup-suffix="+">0
                            </h5>
                            <p class="m-0 fs-16 light-gray">Appointments booked</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="d-flex align-items-center gap-3 gray-box">
                        @include('svg.block')
                        <div class="">
                            <h5 class="dark-blue" data-kt-countup="true" data-kt-countup-value="110000"
                                data-kt-countup-suffix="+">0
                            </h5>
                            <p class="m-0 fs-16 light-gray">Partner businesses</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="professional padding">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h2 class="sora black mb-0 fs-34 semi_bold">🔥 Popular Categories</h2>
                        <a href="{{ route('professional') }}" class="fs-16 sora semi_bold dark-blue">View All <i
                                class="fa-solid fa-chevron-right icon-color ms-2"></i></a>
                    </div>
                    <div class="position-relative pt-5">
                        <!-- Swiper -->
                        <div class="swiper mySwiper category-swipper position-unset">
                            <div class="swiper-wrapper">

                                @foreach ($categories as $category)
                                    <div class="swiper-slide">
                                        <a href="{{ '/services-all/{category?}/{subcategory?}' }}">
                                            <div class="card top-rated-card professional-card">
                                                <div class="card-header border-0 p-0 position-relative">
                                                    <img src="{{ asset('website') . '/' . $category->image ?? '' }}"
                                                        class="top-rated-image" alt="card-image">
                                                </div>
                                                <div class="card-body p-4">
                                                    <p class="fs-14 semi_bold black m-0 ">{{ $category->name ?? '' }}</p>
                                                    <p class="fs-13  regular m-0 dark-blue">21 Professionals<span
                                                            class="normal"></span></p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                            <div class="swiper-button-prev category-prev"></div>
                            <div class="swiper-button-next category-next"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if (auth()->check() && auth()->user()->hasRole('customer'))
        <section class="professional padding-block">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2 class="sora black  fs-34 semi_bold">📌 Nearby Professionals</h2>
                            <a href="{{ 'professional' }}" class="fs-16 sora semi_bold dark-blue">View All <i
                                    class="fa-solid fa-chevron-right icon-color ms-2"></i></a>
                        </div>
                        <div class="position-relative nearby-professionals pt-5">
                            <!-- Swiper -->
                            <div class="swiper mySwiper near_by_swiper position-unset">
                                <div class="swiper-wrapper">
                                    @foreach ($professionals as $professional)
                                        <div class="swiper-slide">
                                            <a href="{{ url('professional_profile/{id}') }}">
                                                <div class="card top-rated-card">
                                                    <div class="card-header border-0 p-0 position-relative">
                                                        @if (isset($professional->profile->banner_image))
                                                            <img src="{{ asset('website') . '/' . $professional->profile->banner_image }}"
                                                                onerror="this.src='{{ asset('website/assets/images/default.png') }}'"
                                                                class="h-100 w-100 top-rated-image" alt="card-image">
                                                        @else
                                                            <img src="{{ asset('website') }}/assets/images/card-image.png"
                                                                onerror="this.src='{{ asset('website/assets/images/default.png') }}'"
                                                                class="h-100 w-100 top-rated-image" alt="card-image">
                                                        @endif
                                                        <div class="fav-icon position-absolute  bottom-10 ">
                                                            <i class="fa-regular fa-heart"></i>
                                                            <input type="hidden" name="wishlist_product_id"
                                                                value="123">
                                                        </div>
                                                    </div>
                                                    <div class="card-body pb-0 p-5">
                                                        <p class="fs-16 semi_bold black m-0 ">
                                                            {{ $professional->profile->company_name ?? '' }}
                                                        </p>
                                                        <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                                class="fa-solid fa-star review-icon mx-1"></i>
                                                            <span class="normal">(440)</span>
                                                        </p>
                                                        <p class="fs-14 regular light-black">
                                                            {{ $professional->profile->location ?? '' }}
                                                        </p>
                                                    </div>
                                                    <div class="card-footer border-0 pt-0 p-5">
                                                        <span class="badge white-badge">Beauty Salon</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </section>
    @endif

    @if (auth()->check() && !auth()->user()->hasRole('customer'))
        <section class="professional padding-block">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2 class="sora black  fs-34 semi_bold">🌟 Top Rated Services</h2>
                            <a href="{{ route('website_services') }}" class="fs-16 sora semi_bold dark-blue">View All <i
                                    class="fa-solid fa-chevron-right icon-color ms-2"></i></a>
                        </div>
                        <div class="position-relative pt-5 ">
                            <!-- Swiper -->
                            <div class="swiper mySwiper top-rated-services-swiper position-unset">
                                <div class="swiper-wrapper">
                                    @php
                                        $images = [
                                            'trainer1.png',
                                            'trainer2.png',
                                            'trainer3.png',
                                            'trainer4.png',
                                            'trainer5.png',
                                            'trainer6.png',
                                            'trainer7.png',
                                            'trainer8.png',
                                            'trainer9.png',
                                            'trainer10.png',
                                            'trainer11.png',
                                            'trainer5.png',
                                        ];
                                    @endphp
                                    @for ($i = 0; $i < 10; $i++)
                                        <div class="swiper-slide">
                                            <div class="card top-rated-card services-card">
                                                <div class="card-header border-0 p-0 position-relative ">
                                                    <img src="{{ asset('website/assets/images/' . $images[$i]) }}"
                                                        class="top-rated-image" alt="card-image">
                                                    <div class="rated-div position-absolute">
                                                        <p class="fs-12 sora semi_bold m-0">@include('svg.rated')TOP
                                                            RATED</p>
                                                    </div>
                                                </div>
                                                <div class="card-body p-5 bg-white">
                                                    <p class="fs-16 semi_bold black m-0 ">General Fitness
                                                        Trainers</p>
                                                    <div class="d-flex gap-2 align-items-center">
                                                        <img src="{{ asset('website') }}/assets/images/service-card.png"
                                                            class="rounded-pill w-25px h-25px" alt="card-image">

                                                        <div>
                                                            <p class="fs-11 semi_bold black m-0">Marshals
                                                                Gents Salon</p>
                                                            <p class="fs-10px sora semi_bold m-0 light-black">
                                                                <i class="fa-solid fa-star review-icon mx-1"></i>5.0
                                                                <span class="normal deep-blue ms-1">(546)</span>
                                                                <span class="light-black opacity-6 ms-1">
                                                                    @include('svg.dot')
                                                                    Al Barsha South, Dubai</span>
                                                            </p>
                                                        </div>

                                                    </div>
                                                </div>

                                                <div class="card-footer border-0 d-flex justify-content-between p-5">
                                                    <div>
                                                        <p class="m-0 fs-16 black bold">$100</p>
                                                        <p class="m-0 fs-14 regular "><i class="fa-regular fa-clock"></i>
                                                            15
                                                            mins</p>
                                                    </div>
                                                    <a href="#!" class="blue-button">Book Now</a>
                                                </div>




                                                <!-- <div class="card-body p-5 bg-white">
                                                                                            <p class="fs-16 semi_bold black m-0 ">General Fitness Trainers</p>
                                                                                            <div class="d-flex gap-2 align-items-center">
                                                                                                <img src="{{ asset('website') }}/assets/images/service-card.png"
                                                                                                    class="rounded-pill w-25px h-25px" alt="card-image">
                                                                                            </div>
                                                                                        </div> -->
                                            </div>
                                        </div>
                                    @endfor
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </section>
    @endif


    @if (auth()->check() && auth()->user()->hasRole('customer'))
        @if (auth()->user()->favoriteProfessionals->count() > 0)
            <section class="professional padding-block">
                <div class="container">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <h2 class="sora black  fs-34 semi_bold">💟 My favorites</h2>
                                <a href="{{ route('favorite_professional') }}"
                                    class="fs-16 sora semi_bold dark-blue">View
                                    All <i class="fa-solid fa-chevron-right icon-color ms-2"></i></a>
                            </div>
                            <div class="row pt-5">
                                @foreach (auth()->user()->favoriteProfessionals as $professional)
                                    <div class="col-md-3">
                                        <div class="card top-rated-card">
                                            <div class="card-header border-0 p-0 position-relative">
                                                <img src="{{ asset('website').'/'.$professional->profile->pic ?? '' }}"
                                                    class="h-100 w-100 top-rated-image" alt="card-image">
                                                <div class="fav-icon position-absolute  bottom-10 ">
                                                    <div id="toast" class="toast-message">Successfully added to
                                                        favourites</div>
                                                    <i class="fa-regular fa-heart review-icon mx-1 "></i>
                                                    <input type="hidden" name="wishlist_product_id" value="123">
                                                </div>
                                            </div>
                                            <div class="card-body pb-0 p-5">
                                                <p class="fs-16 semi_bold black m-0 ">{{ $professional->name ?? '' }}</p>
                                                <p class="fs-15 sora bold m-0 light-black review-icon mx-1">4.5 <i
                                                        class="fa-solid fa-star"></i>
                                                    <span class="normal">(440)</span>
                                                </p>
                                                <p class="fs-14 regular light-black">
                                                    {{ $professional->profile->location ?? '' }}</p>
                                            </div>
                                            <div class="card-footer border-0 pt-0 p-5">
                                                <span
                                                    class="badge white-badge">{{ $professional->profile->company_name ?? '' }}</span>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        @endif
        @php
            $userFriends = auth()->user()->friends()->get();
        @endphp
        @if($userFriends->count() > 0)
        <section class="family padding-block">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="sora black">👪 Friends & Family</h4>
                            <a href="{{ route('friends.index') }}" class="fs-16 sora semi_bold dark-blue">View All <i
                                    class="fa-solid fa-chevron-right icon-color ms-2"></i></a>
                        </div>
                        <div class="row pt-5 row-gap-5">
                            @foreach ($userFriends as $friend)
                                <div class="col-12 col-sm-6 col-lg-3">
                                    <div class="card top-rated-card family-cards">
                                        <div
                                            class="card-header border-0 p-0 position-relative justify-content-center align-items-center">
                                            @if ($friend->type == 'under-13')
                                            <img src="{{ asset('website') . '/' . $friend->profile_pic ?? '' }}"
                                                class="h-90px w-90px object-fit-contain rounded-pill top-rated-image"
                                                alt="card-image">
                                            @else
                                                @if ($friend->user_id == auth()->id() && $friend->friendUser)
                                                <img src="{{ asset('website') . '/' . $friend->friendUser->profile->pic ?? '' }}"
                                                    class="h-90px w-90px object-fit-contain rounded-pill top-rated-image"
                                                    alt="card-image">
                                                @elseif ($friend->friend_user_id == auth()->id() && $friend->user)
                                                <img src="{{ asset('website') . '/' . $friend->user->profile->pic ?? '' }}"
                                                    class="h-90px w-90px object-fit-contain rounded-pill top-rated-image"
                                                    alt="card-image">
                                                @endif
                                            @endif
                                        </div>
                                        <div class="card-body p-0 pt-5">
                                            @if ($friend->type == 'under-13')
                                                <p class="fs-16 semi_bold black text-center">{{ $friend->name ?? '' }}</p>
                                            @else
                                                @if ($friend->user_id == auth()->id() && $friend->friendUser)
                                                    <p class="fs-16 semi_bold black text-center">{{ $friend->friendUser->name ?? '' }}</p>
                                                @elseif ($friend->friend_user_id == auth()->id() && $friend->user)
                                                    <p class="fs-16 semi_bold black text-center">{{ $friend->user->name ?? '' }}</p>
                                                @endif
                                            @endif
                                            <p class="fs-14 regular light-black opacity-6 text-center">
                                                {{ $friend->relationship ?? '' }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </section>
        @endif
    @endif
    <section class="consumers padding-block">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <img src="{{ asset('website') . '/' . $page->section_two_image ?? '' }}"
                        class="h-100 w-lg-500px w-xxl-600px  rounded" alt="consumer-image">
                </div>
                <div class="col-md-6 pt-6 ps-8">
                    <p class="fs-15 regular steel-blue text-uppercase letter-space">{{ $page->section_two_title ?? '' }}
                    </p>
                    <h3 class="sora dark-blue w-500px">
                        {{ $page->section_two_heading ?? '' }}
                    </h3>
                    <p class="fs-16 light-gray w-500px mb-8 mt-5">{{ $page->section_two_description ?? '' }}.</p>

                    <div class="d-flex gap-5 flex-column border-top pt-8">
                        @foreach ($page->details as $detail)
                            <div class="card flex-row shadow-none border-0 border-bottom-0 gap-6">
                                <div class="card-header border-0 border-bottom-0 p-0">
                                    <div class="blue-icon">
                                        <img src="{{ asset('website') . '/' . $detail->image ?? '' }}" alt="icon">
                                    </div>
                                </div>
                                <div class="card-body border-0 border-bottom-0 p-0">
                                    <p class="outfit fs-20 m-0 regular">
                                        {{ $detail->title ?? '' }}
                                    </p>
                                    <p class="fs-16  m-0 light-gray ">
                                        {{ $detail->description ?? '' }}
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="partner business_partner d-flex align-items-center"
        style="background-image: url('{{ asset('website') . '/' . $page->section_three_bg_image ?? '' }}');">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-10 offset-md-2">
                    <p class="white sora mb-2">{{ $page->section_three_title ?? '' }}</p>
                    <h2 class="white sora mb-3">{{ $page->section_three_heading ?? '' }}</h2>
                    <div class="d-flex gap-5">
                        <a href="{{ $page->section_three_button_one_link ?? '' }}"
                            class="button professional-btn">{{ $page->section_three_button_one_text ?? '' }} <i
                                class="fa-solid fa-chevron-right ms-3"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')
    <script>
        $(document).ready(function() {
            CategorySwiper();
            RatedSwipper();
            ServicesSwiper();
            NearBySwiper();
        });

        function CategorySwiper() {
            if ($(".category-swipper").length) {
                var categorySwipper = new Swiper(".category-swipper", {
                    loop: true,
                    slidesPerView: 4,
                    spaceBetween: 20,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                    pagination: {
                        el: ".swiper-pagination",
                    },
                });
            }
        }

        function RatedSwipper() {
            if ($(".top-rated-swiper").length) {
                var topRatedSwiper = new Swiper(".top-rated-swiper", {
                    loop: true,
                    slidesPerView: 4,
                    spaceBetween: 20,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                    pagination: {
                        el: ".swiper-pagination",
                    },
                });
            }
        }

        function NearBySwiper() {
            if ($(".near_by_swiper").length) {
                var topRatedSwiper = new Swiper(".near_by_swiper", {
                    loop: true,
                    slidesPerView: 4,
                    spaceBetween: 20,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                    pagination: {
                        el: ".swiper-pagination",
                    },
                });
            }
        }

        function ServicesSwiper() {
            if ($(".top-rated-services-swiper").length) {
                var topRatedSwiper = new Swiper(".top-rated-services-swiper", {
                    loop: true,
                    slidesPerView: 4,
                    spaceBetween: 20,
                    navigation: {
                        nextEl: ".top-rated-services-swiper .swiper-button-next",
                        prevEl: ".top-rated-services-swiper .swiper-button-prev",
                    },
                });
            }
        }
    </script>
@endpush
@include('layouts.includes.current-location')
