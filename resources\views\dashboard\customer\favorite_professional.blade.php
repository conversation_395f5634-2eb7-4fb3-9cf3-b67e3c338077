@extends('website.layout.master')

@push('css')
    <style>

    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard addfamily padding-block">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-6">
                    <h6 class="sora black my-heading" id="heading-my-fav">💟 My Favorites Professional</h6>
                    <h6 class="sora black my-heading d-none" id="heading-friend-fav">
                        <img src="{{asset('website')}}/assets/images/family.png" alt="Logo" class="h-35px w-35px me-2">
                        Friends Favorite Professional
                    </h6>
                </div>
                <div class="col-md-12">
                    <ul class="nav family-tabs nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active service-tab" id="my-favorite-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-my-favorite" type="button" role="tab"
                                aria-controls="pills-my-favorite" aria-selected="true">My Favorite
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link service-tab" id="friend-favorite-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-friend-favorite" type="button" role="tab"
                                aria-controls="pills-friend-favorite" aria-selected="true">Friend’s Favorite
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="pills-tabContent">

                        <div class="tab-pane fade show active" id="pills-my-favorite" role="tabpanel"
                            aria-labelledby="my-favorite-tab" tabindex="0">
                            <div class="row row-gap-5">
                                @forelse ($professionals as $professional)
                                    <a href="{{ route('professional_profile', $professional->ids) }}">
                                    <div class="col-md-3">
                                        <div class="card top-rated-card">
                                            <div class="card-header border-0 p-0 position-relative">
                                                <img src="{{ asset('website').'/'. $professional->profile->pic ?? '' }}"
                                                    class="h-100 w-100 top-rated-image" alt="card-image">
                                                <div class="fav-icon position-absolute  bottom-10 ">
                                                    <i class="fa-solid fa-heart" style="color: red;"></i>
                                                    <input type="hidden" name="wishlist_product_id" value="123">
                                                </div>
                                            </div>
                                            <div class="card-body pb-0 p-5">
                                                <p class="fs-16 semi_bold black m-0 ">
                                                    {{ $professional->name ?? '' }}
                                                </p>
                                                <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                        class="fa-solid fa-star review-icon mx-1"></i> <span
                                                        class="normal">(440)</span></p>
                                                <p class="fs-14 regular light-black">
                                                    {{ $professional->profile->location ?? '' }},</p>
                                            </div>
                                            <div class="card-footer border-0 pt-0 p-5">
                                                @foreach ($professional->categories as $professionalCategory)
                                                    <span class="badge white-badge">{{ $professionalCategory->name }}</span>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    </a>
                                @empty
                                    <div class="col-12">
                                        <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                            <i class="fas fa-heart fa-4x text-muted mb-4" style="color: #e0e0e0 !important;"></i>
                                            <h5 class="sora semi_bold text-muted mb-2">No Favorite Professionals Yet</h5>
                                            <p class="fs-14 sora regular text-muted text-center">Start exploring and add professionals to your favorites<br>to see them here</p>
                                            <a href="{{ route('home') }}" class="btn btn-primary mt-3 px-4 py-2">
                                                <i class="fas fa-search me-2"></i>Explore Professionals
                                            </a>
                                        </div>
                                    </div>
                                @endforelse
                            </div>
                        </div>

                        <div class="tab-pane fade s" id="pills-friend-favorite" role="tabpanel"
                            aria-labelledby="friend-favoritetab" tabindex="0">
                            <div class="row row-gap-5">
                                @forelse ($friendsFavourites as $friendFavourite)
                                    <a href="{{ route('professional_profile', $friendFavourite->ids) }}">
                                    <div class="col-md-3">
                                        <div class="card top-rated-card">
                                            <div class="card-header border-0 p-0 position-relative">
                                                <img src="{{ asset('website').'/'.$friendFavourite->profile->pic }}"
                                                    class="h-100 w-100 top-rated-image" alt="card-image">
                                                <div class="fav-icon position-absolute  bottom-10 ">
                                                    <i class="fa-solid fa-heart" style="color: red;"></i>
                                                    <input type="hidden" name="wishlist_product_id" value="123">
                                                </div>
                                            </div>
                                            <div class="card-body pb-0 p-5">
                                                <p class="fs-16 semi_bold black m-0 ">
                                                    {{ $friendFavourite->name ?? '' }}
                                                </p>
                                                <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                        class="fa-solid fa-star mx-1 review-icon"></i> <span
                                                        class="normal">(440)</span></p>
                                                <p class="fs-14 regular light-black">
                                                    {{ $friendFavourite->profile->location ?? '' }},</p>
                                            </div>
                                            <div class="card-footer border-0 pt-0 p-5">
                                                @foreach ($friendFavourite->categories as $friendFavouriteCategory)
                                                    <span class="badge white-badge">{{ $friendFavouriteCategory->name }}</span>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    </a>
                                @empty
                                    <div class="col-12">
                                        <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                            <i class="fas fa-users fa-4x text-muted mb-4" style="color: #e0e0e0 !important;"></i>
                                            <h5 class="sora semi_bold text-muted mb-2">No Friend's Favorites Yet</h5>
                                            <p class="fs-14 sora regular text-muted text-center">Your friends haven't added any favorite professionals yet<br>or you haven't connected with friends</p>
                                            <a href="{{ route('friends.index') }}" class="btn btn-outline-primary mt-3 px-4 py-2">
                                                <i class="fas fa-user-friends me-2"></i>Manage Friends
                                            </a>
                                        </div>
                                    </div>
                                @endforelse
                            </div>

                        </div>
                    </div>

                </div>
            </div>

        </div>
    </div>
@endsection
@push('js')

    <script>
        $(document).ready(function () {
            $('button[data-bs-toggle="pill"]').on('shown.bs.tab', function (e) {
                const target = $(e.target).data('bs-target'); // Tab target
                if (target === '#pills-my-favorite') {
                    $('#heading-my-fav').removeClass('d-none');
                    $('#heading-friend-fav').addClass('d-none');
                } else if (target === '#pills-friend-favorite') {
                    $('#heading-my-fav').addClass('d-none');
                    $('#heading-friend-fav').removeClass('d-none');
                }
            });
        });
    </script>

@endpush()
