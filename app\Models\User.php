<?php

namespace App\Models;

use App\Traits\HasUuid;
use App\Models\GoogleCalendar;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, HasUuid;
    //use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array

     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'short_url',
        'is_online',
        'online_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array

     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array

     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'online_at' => 'datetime',
        'is_online' => 'boolean',
    ];

    public function profile()
    {
        return $this->hasOne(Profile::class);
    }
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'user_categories', 'user_id', 'category_id');
    }
    public function userCategories()
    {
        return $this->hasMany(UserCategory::class);
    }
    public function subcategories()
    {
        return $this->belongsToMany(SubCategory::class, 'user_categories', 'user_id', 'subcategory_id');
    }
    public function product_cerficates()
    {
        return $this->belongsToMany(Certification::class, 'user_product_certificates', 'user_id', 'certification_id');
    }
    public function certificates()
    {
        return $this->hasMany(UserCertificate::class);
    }
    function holidays()
    {
        return $this->hasMany(UserHoliday::class)->where("is_custom", 0);
    }
    function customHolidays()
    {
        return $this->hasMany(UserHoliday::class)->where("is_custom", 1);
    }
    function openingHours()
    {
        return $this->hasMany(UserOpeningHour::class)->where('type', 'open');
    }
    function allOpeningHours()
    {
        return $this->hasMany(UserOpeningHour::class);
    }
    public function googleCalendar()
    {
        return $this->hasOne(GoogleCalendar::class);
    }
    public function hasGoogleCalendarConnected()
    {
        return $this->googleCalendar && $this->googleCalendar->google_calendar_connected;
    }

    public function activeSubscription()
    {
        return $this->hasOne(UserSubscription::class)->where('status', 1);
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function service_preferences()
    {
        return $this->belongsToMany(Service::class, 'service_preferences', 'user_id', 'service_id');
    }
    public function services()
    {
        return $this->hasMany(Service::class);
    }

    public function body_specifications()
    {
        return $this->hasMany(UserBodySpecification::class);
    }

    public function getBodySpecificationByType($type)
    {
        return $this->body_specifications()->where('type', $type)->first();
    }

    public function socials()
    {
        return $this->hasMany(UserSocial::class);
    }

    public function galleries()
    {
        return $this->hasMany(UserGallery::class);
    }

    public function introCards()
    {
        return $this->hasMany(UserIntroCard::class);
    }
    public function allHolidays()
    {
        return $this->hasMany(UserHoliday::class);
    }

    static function professional()
    {
        return self::query()
            ->whereIn('status', [1])
            ->whereHas('roles', function ($q) {
                $q->whereIn('name', ['individual', 'business', 'professional']);
            })
            ->has('profile');
    }
    public function friends()
    {
        return $this->hasMany(Friend::class, 'user_id')
            ->where('status', 'accepted')
            ->orWhere(function ($query) {
                $query->where('friend_user_id', $this->id)
                    ->where('status', 'accepted');
            })
            ->with(['friendUser', 'user']);
    }

    public function pendingFriends()
    {
        return $this->hasMany(Friend::class)->where('status', 'pending');
    }

    static function customer()
    {
        return self::query()->whereHas('roles', function ($q) {
            $q->whereIn('name', ['customer']);
        });
    }

    /**
     * Get the staff availabilities for the user.
     */
    public function staffAvailabilities()
    {
        return $this->hasMany(StaffAvailability::class);
    }

    /**
     * Get the staff vacations for the user.
     */
    public function staffVacations()
    {
        return $this->hasMany(StaffVacation::class);
    }

    public function staffs()
    {
        return $this->hasMany(Staff::class);
    }

    public function trustpilotAccount()
    {
        return $this->hasOne(TrustpilotAccount::class);
    }
    public function favoriteProfessionals()
    {
        return $this->belongsToMany(User::class, 'favorite_professionals', 'user_id', 'professional_id');
    }
    public function totalBookings()
    {
        return $this->hasMany(Booking::class)->count();
    }
    public function totalEarnings()
    {
        return $this->hasMany(Booking::class)->sum('total_amount');
    }
    public function lastBookingTime()
    {
        return $this->hasMany(Booking::class)->latest()->first()->created_at ?? null;
    }
    public function isFavorited($professionalId)
    {
        return $this->favoriteProfessionals->contains($professionalId);
    }
    public function existingFriends()
    {
        return $this->hasMany(Friend::class, 'user_id')->where('status', 'accepted');
    }

    /**
     * Get conversations where user is sender
     */
    public function sentConversations()
    {
        return $this->hasMany(Conversation::class, 'sender_id');
    }

    /**
     * Get conversations where user is receiver
     */
    public function receivedConversations()
    {
        return $this->hasMany(Conversation::class, 'receiver_id');
    }

    /**
     * Get all conversations for this user
     */
    public function conversations()
    {
        return Conversation::where('sender_id', $this->id)
            ->orWhere('receiver_id', $this->id)
            ->active()
            ->orderBy('last_message_at', 'desc');
    }

    /**
     * Get messages sent by this user
     */
    public function messages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    /**
     * Get unread messages count for this user
     */
    public function getUnreadMessagesCount()
    {
        return Message::whereHas('conversation', function ($query) {
            $query->where('sender_id', $this->id)
                ->orWhere('receiver_id', $this->id);
        })
            ->where('sender_id', '!=', $this->id)
            ->whereNull('read_at')
            ->count();
    }
}
