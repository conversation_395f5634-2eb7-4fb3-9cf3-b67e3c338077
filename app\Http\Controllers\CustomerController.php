<?php

namespace App\Http\Controllers;

use App\Models\Friend;
use App\Models\Service;
use App\Models\User;
use App\Models\UserBodySpecification;
use App\Models\UserSocial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class CustomerController extends Controller
{
    public function profileSetting()
    {
        $user = auth()->user();
        $services = Service::where('status', 1)->get();
        return view('dashboard.profile_settings.index', compact('user', 'services'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:hair,skin,body,allergy',
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'description' => 'required|string',
            'image' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        $user = auth()->user();
        try {
            DB::beginTransaction();
            $specification = UserBodySpecification::where('user_id', $user->id)->where('type', $request->type)->first();
            if (!$specification) {
                $specification = new UserBodySpecification();
                $specification->user_id = $user->id;
                $specification->type = $request->type;
            }
            if ($request->hasFile('image')) {
                if ($specification->image) {
                    $this->deleteImage($specification->image);
                }
                $specification->image = $this->storeImage('body-specifications', $request->file('image'));
            }
            $specification->name = $request->name;
            $specification->description = $request->description;
            $specification->save();
            DB::commit();
            return redirect()->back()->with([
                'type' => "success",
                'message' => $specification->type . ' type saved successfully',
                'title' => "Success"
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    public function getBodySpecification(Request $request)
    {
        $request->validate([
            'type' => 'required|in:hair,skin,body,allergy',
        ]);

        $user = auth()->user();
        $specification = UserBodySpecification::where('user_id', $user->id)
            ->where('type', $request->type)
            ->first();

        return response()->json([
            'success' => true,
            'data' => $specification
        ]);
    }

    public function updatePersonalInfo(Request $request)
    {
        $user = auth()->user();
        try {
            DB::beginTransaction();
            if ($request->hasFile('image')) {
                if ($user->profile->pic) {
                    $this->deleteImage($user->profile->pic);
                }
                $user->profile->pic = $this->storeImage('profile-images', $request->file('image'));
            }
            $user->name = $request->name;
            $user->profile->city = $request->city;
            $user->profile->country = $request->country;
            $user->profile->location = $request->location;
            $user->profile->phone = $request->phone;
            $user->profile->lat = $request->lat;
            $user->profile->lng = $request->lng;
            $user->save();
            $user->profile->save();
            DB::commit();
            return redirect()->back()->with([
                'type' => "success",
                'message' => 'Personal info updated successfully',
                'title' => "Success"
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    public function updateServicePreferences(Request $request)
    {
        $user = auth()->user();
        try {
            DB::beginTransaction();
            if ($request->has('services')) {
                $user->service_preferences()->sync($request->services);
            }
            DB::commit();
            return redirect()->back()->with([
                'type' => "success",
                'message' => 'Service preferences updated successfully',
                'title' => "Success"
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    public function storeSocials(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'socials' => 'required|array',
            'socials.*.name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'socials.*.link' => ['required', 'regex:' . config('constant.url_regex')],
            'socials.*.image' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        $user = auth()->user();
        try {
            DB::beginTransaction();
            foreach ($request->socials as $social) {
                $userSocial = UserSocial::where('user_id', $user->id)->where('name', $social['name'])->first();
                if (!$userSocial) {
                    $userSocial = new UserSocial();
                    $userSocial->user_id = $user->id;
                }
                if ($social['image']) {
                    if ($userSocial->image) {
                        $this->deleteImage($userSocial->image);
                    }
                    $userSocial->image = $this->storeImage('socials', $social['image']);
                }
                $userSocial->name = $social['name'];
                $userSocial->link = $social['link'];
                $userSocial->save();
            }
            DB::commit();
            return redirect()->back()->with([
                'type' => "success",
                'message' => 'Socials updated successfully',
                'title' => "Success"
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    function favoriteProfessionals(Request $request)
    {
        try {
            $user = User::find(auth()->id());
            $professionalId = $request->professional;
            $isFavorited = $user->favoriteProfessionals()->where('professional_id', $professionalId)->exists();
            if ($isFavorited) {
                $user->favoriteProfessionals()->detach($professionalId);
                return response()->json([
                    'status' => 'success',
                    'action' => 'removed'
                ]);
            } else {
                $user->favoriteProfessionals()->attach($professionalId);
                return response()->json([
                    'status' => 'success',
                    'action' => 'added'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'title' => 'Error!',
                'message' => 'Something went wrong. Please try again.',
                'icon' => 'error'
            ]);
        }
    }
    function favoriteProfessional(Request $request)
    {
        $user = User::where("id", auth()->id())->first();
        $userId = auth()->id();
        $professionals = $user->favoriteProfessionals()->get();
        $friendsFavourites = collect();
        $friends = Friend::where(function ($query) use ($userId) {
            $query->where('user_id', $userId)
                ->orWhere('friend_user_id', $userId);
        })
            ->where('status', 'accepted')
            ->where('type', 'above-13')
            ->get();
        foreach ($friends as $friend) {
            // If current user is the user_id, get favorites from friend_user_id
            if ($friend->user_id == $userId && $friend->friendUser) {
                $friendsFavourites = $friendsFavourites->merge($friend->friendUser->favoriteProfessionals);
            }
            // If current user is the friend_user_id, get favorites from user_id
            elseif ($friend->friend_user_id == $userId && $friend->user) {
                $friendsFavourites = $friendsFavourites->merge($friend->user->favoriteProfessionals);
            }
        }
        $friendsFavourites = $friendsFavourites->unique('id');
        return view('dashboard.customer.favorite_professional', compact('professionals', 'friendsFavourites'));
    }
}
