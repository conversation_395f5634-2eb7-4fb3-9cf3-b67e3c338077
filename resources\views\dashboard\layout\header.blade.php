<!-- Promo Bar -->
<div class="fixed-web-header">

    <div class=" text-white py-1 discount-header" style="display: none !important;">
        <div class="container d-flex justify-content-between align-items-center py-5">
            <ul class="d-flex gap-4 mb-0 px-0 header-icon">
                <li><a href="{{ setting()->facebook ?? '' }}"><img src="{{ asset('website') }}/assets/images/facebook.svg"
                            onerror="this.src='{{ asset('website/assets/images/default.png') }}'" alt="Logo"
                            class="img-fluid"></a></li>
                <li><a href="{{ setting()->whatsapp ?? '' }}"><img
                            src="{{ asset('website') }}/assets/images/whatsapp.svg"
                            onerror="this.src='{{ asset('website/assets/images/default.png') }}'" alt="Logo"
                            class="img-fluid"></a></li>
                <li><a href="{{ setting()->youtube ?? '' }}"><img src="{{ asset('website') }}/assets/images/youtube.svg"
                            onerror="this.src='{{ asset('website/assets/images/default.png') }}'" alt="Logo"
                            class="img-fluid"></a></li>
            </ul>
            <div class="d-flex gap-4">
                <a href="mailto:{{ setting()->email ?? '' }}" class="fs-13 normal white"><i
                        class="fa-solid fa-envelope me-2" style="color: #ffffff;"></i> {{ setting()->email ?? '' }}</a>
                <a href="tel:{{ setting()->phone ?? '' }}" class="fs-13 normal white"> <i
                        class="fa-solid fa-phone me-2" style="color: #ffffff;"></i> {{ setting()->phone ?? '' }}</a>
            </div>
        </div>
    </div>

    <!-- Main Navbar -->
    <nav class="navbar navbar-expand-lg bg-white header">
        <div class="container py-2">
            <!-- Logo -->
            <div class="d-flex gap-2  align-items-center">
                <a class="navbar-brand" href="{{ url('/') }}">
                    <img src="{{ asset('website') . '/' . setting()->logo }}" alt="Logo" class="img-fluid"
                        style="height: 37px;">
                </a>
                @if (auth()->check() && !auth()->user()->hasRole('admin'))
                    <!-- <img src="{{ asset('website') . '/' . setting()->logo }}" alt="Logo" class="img-fluid"
                        style="height: 20px;"> -->

                    <div id="user-selected-location">
                        {{ auth()->user()->profile->location ?? 'No Location Found' }}
                    </div>
                    {{-- <div class="custom-select-location" style="width:200px;">
                         <select>
                            <option value="0">{{ auth()->user()->profile->location ?? "No Location Found" }}</option>
                        </select> 
                    </div>--}}
                @endif
            </div>

            <!-- Toggler for mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>


            <!-- Navbar content -->
            <div class="collapse navbar-collapse flex-end align-items-center navbar-header gap-4" id="mainNavbar">
                @if (auth()->check() && auth()->user()->hasRole('individual'))
                    <div class="app-navbar-item ms-1 user-info">
                        <i class="fas fa-star "> <span class="user-name sora fs-12"> INDIVIDUAL </span> </i>
                    </div>
                @elseif(auth()->check() && auth()->user()->hasRole('business'))
                    <div class="app-navbar-item ms-1 user-info">
                        <i class="fas fa-star "> <span class="user-name sora fs-12"> BUSINESS </span> </i>
                    </div>
                @endif

                <div class="app-navbar-item ms-1 ">
                    <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-15px h-15px position-relative"
                        data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                        data-kt-menu-placement="bottom-end" id="kt_menu_item_wow">
                        <i class="far fa-bell"></i>
                        <span id="notification-counter"
                            class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                            style="display: none; font-size: 10px; min-width: 18px; height: 18px; line-height: 18px;">
                            0
                        </span>
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                        <span class="path4"></span>
                        </i>
                    </div>
                    <div class="menu notification-dropdown menu-sub menu-sub-dropdown menu-column w-350px w-lg-375px p-5"
                        data-kt-menu="true" id="kt_menu_notifications">

                        <p class="fs-16 bold">Notifications</p>

                        <div id="notification-list">
                            @php
                                $recentNotifications = auth()->check()
                                    ? \App\Models\Notification::where('user_id', auth()->id())
                                        ->with('user')
                                        ->latest()
                                        ->take(4)
                                        ->get()
                                    : collect();
                            @endphp

                            @forelse ($recentNotifications as $notification)
                                <div class="d-flex align-items-center gap-3 justify-content-center border-bottom mb-5 pb-3"
                                    data-notification-id="{{ $notification->id }}" style="cursor: pointer;">
                                    <img src="{{ asset('website') . '/' . ($notification->notification_image ?? ($notification->user->profile->pic ?? 'no_avatar.jpg')) }}"
                                        alt="Logo" class="img-fluid"
                                        style="height: 50px; width: 50px; object-fit: cover; border-radius: 50%;">
                                    <div>
                                        <p class="fs-13 mb-0">{{ $notification->title }} -
                                            {{ Str::limit($notification->message, 50) }}</p>
                                        <p class="fs-12 mb-0">{{ $notification->created_at->diffForHumans() }}</p>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-3">
                                    <p class="fs-13 mb-0 text-muted">No notifications yet</p>
                                </div>
                            @endforelse
                        </div>

                        <a href="{{ route('notification') }}" class="see-all-btn"> See All</a>
                    </div>
                </div>

                <div class="app-navbar-item ms-1">
                    <a href="{{ route('chats.index') }}"
                        class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px position-relative">
                        <i class="far fa-envelope"></i>
                        <span id="envelope-counter"
                            class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                            style="display: none; font-size: 10px; min-width: 18px; height: 18px; line-height: 18px;">
                            0
                        </span>
                    </a>
                </div>

                <!-- <div lass="app-navbar-item ms-1">
                    <i class="far fa-question-circle mt-1"></i>
                </div> -->

                <div class="app-navbar-item ms-1 " id="kt_header_user_menu_toggle">
                    <div class="cursor-pointer symbol symbol-35px "
                        data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                        data-kt-menu-placement="bottom-end">
                        @if (!auth()->user()->profile || Auth::user()->profile->pic == null)
                            <img src="{{ asset('website') }}/assets/media/avatars/blank.png" class="rounded-pill"
                                alt="user" />
                        @else
                            <img alt="Logo" src="{{ asset('website') . '/' . auth()->user()->profile->pic }}"
                                onerror="this.src='{{ asset('website/assets/images/default.png') }}'" />
                        @endif
                    </div>
                    <div class="menu  menu-sub menu-sub-dropdown right-sidebar-menus menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px"
                        data-kt-menu="true">
                        <div class="menu-item px-3">
                            <div class="menu-content d-flex align-items-center px-3">
                                <div class="symbol symbol-50px me-5">
                                    @if (!auth()->user()->profile || auth()->user()->profile->pic == null)
                                        <img src="{{ asset('website') }}/assets/media/avatars/blank.png"
                                            class="rounded-pill" alt="user" />
                                    @else
                                        <img alt="Logo"
                                            src="{{ asset('website') . '/' . auth()->user()->profile->pic }}" />
                                    @endif
                                </div>
                                <div class="d-flex flex-column">
                                    <div class="fw-bold d-flex align-items-center fs-5">{{ Auth::user()->name ?? '' }}
                                    </div>
                                    <a href="#"
                                        class="fw-semibold deep-blue fs-7">{{ Auth::user()->email ?? '' }}</a>
                                </div>
                            </div>
                        </div>
                        <div class="separator my-2"></div>
                        @if (auth()->check() && auth()->user()->hasRole('customer'))
                            <div class="menu-item px-3">
                                <a href="{{ route('profile_setting') }}" class="menu-link px-5">Profile</a>
                            </div>
                        @else
                            <div class="menu-item px-3">
                                <a href="{{ route('profile_settings') }}" class="menu-link px-5">Profile</a>
                            </div>
                        @endif
                        <div class="menu-item px-3">
                            <a href="{{ route('setting') }}" class="menu-link px-5">Settings</a>
                        </div>
                        <div class="separator my-2"></div>
                        <div class="menu-item px-3">
                            <a href="{{ url('logout') }}" class="menu-link px-5 logout">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="bg-white border-top header-items">
        <div class="container">
            <ul class="nav justify-content-start py-4 gap-7">
                @if (auth()->check() &&
                        auth()->user()->hasAnyRole(['professional', 'individual', 'business']))
                    <!-- Dashboard Menu Item -->
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('dashboard')) active @endif"
                            href="{{ route('dashboard') }}">
                            Dashboard
                        </a>
                    </li>

                    <!-- For Business Role, show Staff first, then Booking -->
                    @if (auth()->user()->hasRole('business'))
                        <!-- Staff Menu Item -->
                        <li class="nav-item">
                            <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('staffs') || request()->is('staffs/create') || request()->is('staffs/{staff}')) active @endif"
                                href="{{ route('staffs.index') }}">
                                Staff
                            </a>
                        </li>
                    @endif
                    <!-- Booking Menu Item -->
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('booking')) active @endif"
                            href="{{ route('booking') }}">
                            Booking
                        </a>
                    </li>

                    <!-- Chats Menu Item -->
                    {{-- <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('chats*')) active @endif"
                            href="{{ route('chats.index') }}">
                            Chats
                        </a>
                    </li> --}}

                    <!-- Common Menu Items for Both Individual and Business -->
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->routeIs('services.*')) active @endif"
                            href="{{ route('services.index') }}">
                            Services
                        </a>

                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('earning')) active @endif"
                            href="{{ route('earning') }}">
                            Earnings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('subscriptions')) active @endif"
                            href="{{ route('subscriptions.index') }}">
                            Subscription
                        </a>
                    </li>
                    @can('discountcoupons-list')
                        <li class="nav-item">
                            <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('discount-coupons') || request()->is('discount-coupons/create')) active @endif"
                                href="{{ route('discount-coupons.index') }}">
                                Discount & Coupon
                            </a>
                        </li>
                    @endcan

                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('analytics')) active @endif"
                            href="{{ route('business_analytics') }}">
                            Analytics
                        </a>
                    </li>
                @endif
                <!-- Admin Role Menu -->
                <!-- @if (auth()->check() &&
                        auth()->user()->hasRole(['admin']))
<div class="position-relative">
                        <div class="swiper mySwiper header-swiper">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('dashboard')) active @endif"
                                            href="{{ route('dashboard') }}">
                                            Dashboard
                                        </a>
                                    </li>
                                </div>
                                @can('categories-list')
    <div class="swiper-slide">
                                            <li class="nav-item">
                                                <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('categories')) active @endif"
                                                    href="{{ route('categories.index') }}">
                                                    Categories
                                                </a>
                                            </li>
                                        </div>
@endcan

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('professionals')) active @endif"
                                            href="{{ route('professionals') }}">
                                            Professionals
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('customers')) active @endif"
                                            href="{{ route('customers') }}">
                                            Customers
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('booking')) active @endif"
                                            href="{{ route('booking') }}">
                                            Bookings
                                        </a>
                                    </li>
                                </div>


                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('refund_request')) active @endif"
                                            href="{{ route('refund_request') }}">
                                            Refund Requests
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('wallet')) active @endif"
                                            href="{{ route('wallet') }}">
                                            Wallet
                                        </a>
                                    </li>
                                </div>

                                @can('vatmanagements-list')
    <div class="swiper-slide">
                                            <li class="nav-item">
                                                <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('vatmanagements')) active @endif"
                                                    href="{{ route('vatmanagements.index') }}">
                                                    VAT Management
                                                </a>
                                            </li>
                                        </div>
@endcan

                                @can('discountcoupons-list')
    <div class="swiper-slide">
                                            <li class="nav-item">
                                                <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('discount-coupons', 'discount-coupons/create')) active @endif"
                                                    href="{{ route('discount-coupons.index') }}">
                                                    Discount & Coupon
                                                </a>
                                            </li>
                                        </div>
@endcan

                                @can('subscriptions-list')
    <div class="swiper-slide">
                                            <li class="nav-item">
                                                <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('subscriptions')) active @endif"
                                                    href="{{ route('subscriptions.index') }}">
                                                    Subscription Management
                                                </a>
                                            </li>
                                        </div>
@endcan

                                @can('holidays-list')
    <div class="swiper-slide">
                                            <li class="nav-item">
                                                <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('holidays')) active @endif"
                                                    href="{{ route('holidays.index') }}">
                                                    Holidays
                                                </a>
                                            </li>
                                        </div>
@endcan

                                @can('certifications-list')
    <div class="swiper-slide">
                                            <li class="nav-item">
                                                <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('certifications')) active @endif"
                                                    href="{{ route('certifications.index') }}">
                                                    Certifications
                                                </a>
                                            </li>
                                        </div>
@endcan

                                @can('cms-list')
    <div class="swiper-slide">
                                            <li class="nav-item">
                                                <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('cms/home')) active @endif"
                                                    href="{{ route('cms.home') }}">
                                                    CMS
                                                </a>
                                            </li>
                                        </div>
@endcan

                                @can('countries-list')
    <div class="swiper-slide">
                                            <li class="nav-item">
                                                <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('countries')) active @endif"
                                                    href="{{ route('countries.index') }}">
                                                    Countries
                                                </a>
                                            </li>
                                        </div>
@endcan

                                @can('settings-list')
    <div class="swiper-slide">
                                            <li class="nav-item">
                                                <a class="nav-link fs-14 normal sora semi_bold header-active @if (request()->is('settings')) active @endif"
                                                    href="{{ route('settings.index') }}">
                                                    Settings
                                                </a>
                                            </li>
                                        </div>
@endcan
                            </div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                    </div>
@endif -->

                @guest()
                    <li class="nav-item ms-auto"><a class="nav-link  deep-blue fs-14 sora semi_bold"
                            href="{{ url('register') }}">Become a professional →</a>
                    </li>
                @endguest
            </ul>
        </div>
    </div>
</div>
